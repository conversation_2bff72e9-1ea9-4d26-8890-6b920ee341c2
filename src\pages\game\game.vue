<template>
  <view class="game-container">
    <!-- 游戏画布 -->
    <canvas 
      canvas-id="gameCanvas" 
      class="game-canvas"
      @touchstart="onTouchStart"
      @touchmove="onTouchMove"
      @touchend="onTouchEnd"
    ></canvas>
    
    <!-- 游戏UI -->
    <view class="game-ui">
      <view class="score-panel">
        <text class="score-text">分数: {{score}}</text>
        <text class="lives-text">生命: {{lives}}</text>
        <text class="bombs-text">炸弹: {{bombs}}</text>
      </view>
      
      <view class="control-panel">
        <button class="bomb-btn" @click="useBomb" :disabled="bombs <= 0">炸弹</button>
        <button class="pause-btn" @click="togglePause">{{isPaused ? '继续' : '暂停'}}</button>
      </view>
    </view>
    
    <!-- 游戏结束界面 -->
    <view v-if="gameOver" class="game-over">
      <view class="game-over-content">
        <text class="game-over-title">游戏结束</text>
        <text class="final-score">最终分数: {{score}}</text>
        <button class="restart-btn" @click="restartGame">重新开始</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 游戏状态
      gameRunning: false,
      gameOver: false,
      isPaused: false,
      score: 0,
      lives: 3,
      bombs: 3,
      
      // 画布相关
      ctx: null,
      canvasWidth: 0,
      canvasHeight: 0,
      
      // 游戏对象
      player: {
        x: 0,
        y: 0,
        width: 60,
        height: 60,
        speed: 5
      },
      bullets: [],
      enemies: [],
      powerUps: [],
      explosions: [],
      
      // 游戏参数
      lastFired: 0,
      fireRate: 150,
      enemySpawnRate: 1000,
      lastEnemySpawn: 0,
      powerLevel: 1,
      
      // 动画帧
      animationFrame: null,
      lastTime: 0
    }
  },
  
  onLoad() {
    this.initGame()
  },
  
  onUnload() {
    this.stopGame()
  },
  
  methods: {
    // 初始化游戏
    initGame() {
      const query = uni.createSelectorQuery().in(this)
      query.select('.game-canvas').boundingClientRect(data => {
        this.canvasWidth = data.width
        this.canvasHeight = data.height
        
        // 初始化玩家位置
        this.player.x = this.canvasWidth / 2 - this.player.width / 2
        this.player.y = this.canvasHeight - this.player.height - 50
        
        // 获取画布上下文
        this.ctx = uni.createCanvasContext('gameCanvas', this)
        
        this.startGame()
      }).exec()
    },
    
    // 开始游戏
    startGame() {
      this.gameRunning = true
      this.gameOver = false
      this.gameLoop()
    },
    
    // 停止游戏
    stopGame() {
      this.gameRunning = false
      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame)
      }
    },
    
    // 游戏主循环
    gameLoop(currentTime = 0) {
      if (!this.gameRunning || this.isPaused) {
        if (this.gameRunning) {
          this.animationFrame = requestAnimationFrame(this.gameLoop)
        }
        return
      }
      
      const deltaTime = currentTime - this.lastTime
      this.lastTime = currentTime
      
      this.update(currentTime, deltaTime)
      this.render()
      
      this.animationFrame = requestAnimationFrame(this.gameLoop)
    },
    
    // 更新游戏逻辑
    update(currentTime, deltaTime) {
      // 自动射击
      if (currentTime - this.lastFired > this.fireRate) {
        this.fireBullet()
        this.lastFired = currentTime
      }
      
      // 生成敌机
      if (currentTime - this.lastEnemySpawn > this.enemySpawnRate) {
        this.spawnEnemy()
        this.lastEnemySpawn = currentTime
      }
      
      // 更新子弹
      this.updateBullets()
      
      // 更新敌机
      this.updateEnemies()
      
      // 更新道具
      this.updatePowerUps()
      
      // 更新爆炸效果
      this.updateExplosions()
      
      // 碰撞检测
      this.checkCollisions()
      
      // 检查游戏结束
      if (this.lives <= 0) {
        this.endGame()
      }
    },
    
    // 渲染游戏画面
    render() {
      // 清空画布
      this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)
      
      // 绘制背景
      this.ctx.setFillStyle('#000033')
      this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
      
      // 绘制玩家
      this.drawPlayer()
      
      // 绘制子弹
      this.bullets.forEach(bullet => this.drawBullet(bullet))
      
      // 绘制敌机
      this.enemies.forEach(enemy => this.drawEnemy(enemy))
      
      // 绘制道具
      this.powerUps.forEach(powerUp => this.drawPowerUp(powerUp))
      
      // 绘制爆炸效果
      this.explosions.forEach(explosion => this.drawExplosion(explosion))
      
      this.ctx.draw()
    },
    
    // 绘制玩家
    drawPlayer() {
      this.ctx.setFillStyle('#00AAFF')
      this.ctx.fillRect(this.player.x, this.player.y, this.player.width, this.player.height)
      
      // 绘制玩家细节
      this.ctx.setFillStyle('#FFFFFF')
      this.ctx.fillRect(this.player.x + 25, this.player.y + 10, 10, 30)
    },
    
    // 绘制子弹
    drawBullet(bullet) {
      this.ctx.setFillStyle('#FFFF00')
      this.ctx.fillRect(bullet.x, bullet.y, bullet.width, bullet.height)
    },
    
    // 绘制敌机
    drawEnemy(enemy) {
      this.ctx.setFillStyle('#FF0000')
      this.ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height)
      
      // 绘制敌机细节
      this.ctx.setFillStyle('#FFFFFF')
      this.ctx.fillRect(enemy.x + 15, enemy.y + 20, 10, 15)
    },
    
    // 绘制道具
    drawPowerUp(powerUp) {
      this.ctx.setFillStyle(powerUp.type === 'power' ? '#00FF00' : '#FF00FF')
      this.ctx.fillRect(powerUp.x, powerUp.y, powerUp.width, powerUp.height)
    },
    
    // 绘制爆炸效果
    drawExplosion(explosion) {
      const alpha = 1 - (explosion.age / explosion.maxAge)
      this.ctx.setGlobalAlpha(alpha)
      this.ctx.setFillStyle('#FF8800')
      this.ctx.fillRect(explosion.x - explosion.size/2, explosion.y - explosion.size/2, explosion.size, explosion.size)
      this.ctx.setGlobalAlpha(1)
    },
    
    // 射击
    fireBullet() {
      const bullet = {
        x: this.player.x + this.player.width/2 - 2,
        y: this.player.y,
        width: 4,
        height: 10,
        speed: 8
      }
      this.bullets.push(bullet)
      
      // 双重射击
      if (this.powerLevel >= 2) {
        this.bullets.push({
          x: this.player.x + 10,
          y: this.player.y,
          width: 4,
          height: 10,
          speed: 8
        })
        this.bullets.push({
          x: this.player.x + this.player.width - 14,
          y: this.player.y,
          width: 4,
          height: 10,
          speed: 8
        })
      }
    },
    
    // 生成敌机
    spawnEnemy() {
      const enemy = {
        x: Math.random() * (this.canvasWidth - 40),
        y: -40,
        width: 40,
        height: 40,
        speed: 2 + Math.random() * 3,
        health: 1
      }
      this.enemies.push(enemy)
    },
    
    // 更新子弹
    updateBullets() {
      this.bullets = this.bullets.filter(bullet => {
        bullet.y -= bullet.speed
        return bullet.y > -bullet.height
      })
    },
    
    // 更新敌机
    updateEnemies() {
      this.enemies = this.enemies.filter(enemy => {
        enemy.y += enemy.speed
        return enemy.y < this.canvasHeight
      })
    },
    
    // 更新道具
    updatePowerUps() {
      this.powerUps = this.powerUps.filter(powerUp => {
        powerUp.y += powerUp.speed
        return powerUp.y < this.canvasHeight
      })
    },
    
    // 更新爆炸效果
    updateExplosions() {
      this.explosions = this.explosions.filter(explosion => {
        explosion.age++
        explosion.size += 2
        return explosion.age < explosion.maxAge
      })
    },
    
    // 碰撞检测
    checkCollisions() {
      // 子弹击中敌机
      this.bullets.forEach((bullet, bulletIndex) => {
        this.enemies.forEach((enemy, enemyIndex) => {
          if (this.isColliding(bullet, enemy)) {
            this.bullets.splice(bulletIndex, 1)
            this.enemies.splice(enemyIndex, 1)
            this.score += 10
            this.createExplosion(enemy.x + enemy.width/2, enemy.y + enemy.height/2)
            
            // 随机掉落道具
            if (Math.random() < 0.2) {
              this.spawnPowerUp(enemy.x, enemy.y)
            }
          }
        })
      })
      
      // 玩家碰撞敌机
      this.enemies.forEach((enemy, enemyIndex) => {
        if (this.isColliding(this.player, enemy)) {
          this.enemies.splice(enemyIndex, 1)
          this.lives--
          this.powerLevel = 1
          this.createExplosion(this.player.x + this.player.width/2, this.player.y + this.player.height/2)
        }
      })
      
      // 玩家收集道具
      this.powerUps.forEach((powerUp, powerUpIndex) => {
        if (this.isColliding(this.player, powerUp)) {
          this.powerUps.splice(powerUpIndex, 1)
          if (powerUp.type === 'power' && this.powerLevel < 3) {
            this.powerLevel++
          } else if (powerUp.type === 'bomb') {
            this.bombs++
          }
        }
      })
    },
    
    // 碰撞检测辅助函数
    isColliding(obj1, obj2) {
      return obj1.x < obj2.x + obj2.width &&
             obj1.x + obj1.width > obj2.x &&
             obj1.y < obj2.y + obj2.height &&
             obj1.y + obj1.height > obj2.y
    },
    
    // 创建爆炸效果
    createExplosion(x, y) {
      this.explosions.push({
        x: x,
        y: y,
        size: 10,
        age: 0,
        maxAge: 20
      })
    },
    
    // 生成道具
    spawnPowerUp(x, y) {
      const powerUp = {
        x: x,
        y: y,
        width: 20,
        height: 20,
        speed: 3,
        type: Math.random() < 0.7 ? 'power' : 'bomb'
      }
      this.powerUps.push(powerUp)
    },
    
    // 使用炸弹
    useBomb() {
      if (this.bombs > 0) {
        this.bombs--
        this.enemies.forEach(enemy => {
          this.createExplosion(enemy.x + enemy.width/2, enemy.y + enemy.height/2)
          this.score += 5
        })
        this.enemies = []
      }
    },
    
    // 暂停/继续游戏
    togglePause() {
      this.isPaused = !this.isPaused
    },
    
    // 重新开始游戏
    restartGame() {
      this.score = 0
      this.lives = 3
      this.bombs = 3
      this.powerLevel = 1
      this.bullets = []
      this.enemies = []
      this.powerUps = []
      this.explosions = []
      this.gameOver = false
      this.startGame()
    },
    
    // 结束游戏
    endGame() {
      this.gameRunning = false
      this.gameOver = true
    },
    
    // 触摸事件处理
    onTouchStart(e) {
      // 处理触摸开始
    },
    
    onTouchMove(e) {
      if (e.touches && e.touches.length > 0) {
        const touch = e.touches[0]
        this.player.x = touch.x - this.player.width / 2
        
        // 限制玩家在画布内
        if (this.player.x < 0) this.player.x = 0
        if (this.player.x > this.canvasWidth - this.player.width) {
          this.player.x = this.canvasWidth - this.player.width
        }
      }
    },
    
    onTouchEnd(e) {
      // 处理触摸结束
    }
  }
}
</script>

<style scoped>
.game-container {
  width: 100%;
  height: 100vh;
  position: relative;
  background: #000;
}

.game-canvas {
  width: 100%;
  height: 100%;
}

.game-ui {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  pointer-events: none;
}

.score-panel {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  background: rgba(0, 0, 0, 0.5);
}

.score-text, .lives-text, .bombs-text {
  color: white;
  font-size: 28rpx;
}

.control-panel {
  position: absolute;
  bottom: 40rpx;
  right: 40rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  pointer-events: auto;
}

.bomb-btn, .pause-btn {
  padding: 20rpx 40rpx;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.bomb-btn:disabled {
  background: #ccc;
}

.game-over {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.game-over-content {
  background: white;
  padding: 80rpx;
  border-radius: 20rpx;
  text-align: center;
}

.game-over-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
}

.final-score {
  font-size: 36rpx;
  margin-bottom: 60rpx;
  color: #666;
}

.restart-btn {
  padding: 30rpx 60rpx;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 32rpx;
}
</style>
