// 游戏音频管理器
export class GameAudioManager {
  constructor(enabled = true) {
    this.enabled = enabled
    this.sounds = {}
    this.musicVolume = 0.7
    this.soundVolume = 0.8
    this.currentMusic = null
    this.isH5 = false
    
    // 检测平台
    // #ifdef H5
    this.isH5 = true
    // #endif
    
    this.init()
  }
  
  // 初始化音频系统
  init() {
    if (this.isH5) {
      this.initH5Audio()
    } else {
      this.initMiniProgramAudio()
    }
  }
  
  // 初始化H5音频
  initH5Audio() {
    // H5版本使用Web Audio API或HTML5 Audio
    this.audioContext = null
    
    // 尝试创建AudioContext
    try {
      window.AudioContext = window.AudioContext || window.webkitAudioContext
      this.audioContext = new AudioContext()
    } catch (e) {
      console.warn('Web Audio API not supported, falling back to HTML5 Audio')
    }
    
    // 预加载音效文件
    this.preloadSounds()
  }
  
  // 初始化小程序音频
  initMiniProgramAudio() {
    // 小程序版本使用uni.createInnerAudioContext
    this.audioContexts = {}
    this.preloadSounds()
  }
  
  // 预加载音效
  preloadSounds() {
    const soundFiles = {
      shoot: '/static/sounds/shoot.mp3',
      explosion: '/static/sounds/explosion.mp3',
      powerup: '/static/sounds/powerup.mp3',
      bomb: '/static/sounds/bomb.mp3',
      enemy_hit: '/static/sounds/enemy_hit.mp3',
      player_hit: '/static/sounds/player_hit.mp3',
      level_complete: '/static/sounds/level_complete.mp3',
      game_over: '/static/sounds/game_over.mp3',
      button_click: '/static/sounds/button_click.mp3'
    }
    
    const musicFiles = {
      level1_bg: '/static/music/level1_bg.mp3',
      level2_bg: '/static/music/level2_bg.mp3',
      level3_bg: '/static/music/level3_bg.mp3',
      boss_bg: '/static/music/boss_bg.mp3',
      final_bg: '/static/music/final_bg.mp3',
      menu_bg: '/static/music/menu_bg.mp3'
    }
    
    // 加载音效
    Object.keys(soundFiles).forEach(key => {
      this.loadSound(key, soundFiles[key], 'sound')
    })
    
    // 加载音乐
    Object.keys(musicFiles).forEach(key => {
      this.loadSound(key, musicFiles[key], 'music')
    })
  }
  
  // 加载单个音频文件
  loadSound(key, url, type) {
    if (this.isH5) {
      this.loadH5Sound(key, url, type)
    } else {
      this.loadMiniProgramSound(key, url, type)
    }
  }
  
  // H5加载音频
  loadH5Sound(key, url, type) {
    const audio = new Audio()
    audio.src = url
    audio.preload = 'auto'
    audio.volume = type === 'music' ? this.musicVolume : this.soundVolume
    
    // 处理加载错误
    audio.onerror = () => {
      console.warn(`Failed to load audio: ${url}`)
    }
    
    this.sounds[key] = {
      audio,
      type,
      loaded: false
    }
    
    audio.addEventListener('canplaythrough', () => {
      this.sounds[key].loaded = true
    })
  }
  
  // 小程序加载音频
  loadMiniProgramSound(key, url, type) {
    const audio = uni.createInnerAudioContext()
    audio.src = url
    audio.volume = type === 'music' ? this.musicVolume : this.soundVolume
    
    // 处理加载错误
    audio.onError = (error) => {
      console.warn(`Failed to load audio: ${url}`, error)
    }
    
    this.sounds[key] = {
      audio,
      type,
      loaded: false
    }
    
    audio.onCanplay = () => {
      this.sounds[key].loaded = true
    }
  }
  
  // 播放音效
  playSound(key, volume = 1.0) {
    if (!this.enabled || !this.sounds[key]) {
      return
    }
    
    const sound = this.sounds[key]
    if (!sound.loaded) {
      return
    }
    
    try {
      if (this.isH5) {
        this.playH5Sound(sound, volume)
      } else {
        this.playMiniProgramSound(sound, volume)
      }
    } catch (error) {
      console.warn(`Error playing sound ${key}:`, error)
    }
  }
  
  // H5播放音效
  playH5Sound(sound, volume) {
    const audio = sound.audio
    audio.volume = (sound.type === 'music' ? this.musicVolume : this.soundVolume) * volume
    
    // 重置播放位置
    audio.currentTime = 0
    
    // 播放音频
    const playPromise = audio.play()
    if (playPromise !== undefined) {
      playPromise.catch(error => {
        console.warn('Audio play failed:', error)
      })
    }
  }
  
  // 小程序播放音效
  playMiniProgramSound(sound, volume) {
    const audio = sound.audio
    audio.volume = (sound.type === 'music' ? this.musicVolume : this.soundVolume) * volume
    
    // 停止当前播放
    audio.stop()
    
    // 播放音频
    audio.play()
  }
  
  // 播放背景音乐
  playMusic(key, loop = true) {
    if (!this.enabled || !this.sounds[key]) {
      return
    }
    
    // 停止当前音乐
    this.stopMusic()
    
    const music = this.sounds[key]
    if (!music.loaded || music.type !== 'music') {
      return
    }
    
    try {
      if (this.isH5) {
        music.audio.loop = loop
        music.audio.volume = this.musicVolume
        const playPromise = music.audio.play()
        if (playPromise !== undefined) {
          playPromise.catch(error => {
            console.warn('Music play failed:', error)
          })
        }
      } else {
        music.audio.loop = loop
        music.audio.volume = this.musicVolume
        music.audio.play()
      }
      
      this.currentMusic = music
    } catch (error) {
      console.warn(`Error playing music ${key}:`, error)
    }
  }
  
  // 停止背景音乐
  stopMusic() {
    if (this.currentMusic) {
      try {
        if (this.isH5) {
          this.currentMusic.audio.pause()
          this.currentMusic.audio.currentTime = 0
        } else {
          this.currentMusic.audio.stop()
        }
      } catch (error) {
        console.warn('Error stopping music:', error)
      }
      
      this.currentMusic = null
    }
  }
  
  // 暂停背景音乐
  pauseMusic() {
    if (this.currentMusic) {
      try {
        if (this.isH5) {
          this.currentMusic.audio.pause()
        } else {
          this.currentMusic.audio.pause()
        }
      } catch (error) {
        console.warn('Error pausing music:', error)
      }
    }
  }
  
  // 恢复背景音乐
  resumeMusic() {
    if (this.currentMusic) {
      try {
        if (this.isH5) {
          const playPromise = this.currentMusic.audio.play()
          if (playPromise !== undefined) {
            playPromise.catch(error => {
              console.warn('Music resume failed:', error)
            })
          }
        } else {
          this.currentMusic.audio.play()
        }
      } catch (error) {
        console.warn('Error resuming music:', error)
      }
    }
  }
  
  // 设置音效音量
  setSoundVolume(volume) {
    this.soundVolume = Math.max(0, Math.min(1, volume))
    
    // 更新所有音效的音量
    Object.keys(this.sounds).forEach(key => {
      const sound = this.sounds[key]
      if (sound.type === 'sound') {
        sound.audio.volume = this.soundVolume
      }
    })
  }
  
  // 设置音乐音量
  setMusicVolume(volume) {
    this.musicVolume = Math.max(0, Math.min(1, volume))
    
    // 更新所有音乐的音量
    Object.keys(this.sounds).forEach(key => {
      const sound = this.sounds[key]
      if (sound.type === 'music') {
        sound.audio.volume = this.musicVolume
      }
    })
  }
  
  // 启用/禁用音频
  setEnabled(enabled) {
    this.enabled = enabled
    
    if (!enabled) {
      this.stopMusic()
    }
  }
  
  // 获取音频状态
  isEnabled() {
    return this.enabled
  }
  
  // 清理资源
  cleanup() {
    this.stopMusic()
    
    Object.keys(this.sounds).forEach(key => {
      const sound = this.sounds[key]
      try {
        if (this.isH5) {
          sound.audio.pause()
          sound.audio.src = ''
        } else {
          sound.audio.destroy()
        }
      } catch (error) {
        console.warn(`Error cleaning up sound ${key}:`, error)
      }
    })
    
    this.sounds = {}
    this.currentMusic = null
    
    if (this.audioContext) {
      this.audioContext.close()
    }
  }
}

// 创建全局音频管理器实例
let globalAudioManager = null

export function getGlobalAudioManager() {
  if (!globalAudioManager) {
    const settings = uni.getStorageSync('gameHall_settings') || {}
    globalAudioManager = new GameAudioManager(settings.soundEnabled !== false)
  }
  return globalAudioManager
}

export function destroyGlobalAudioManager() {
  if (globalAudioManager) {
    globalAudioManager.cleanup()
    globalAudioManager = null
  }
}
