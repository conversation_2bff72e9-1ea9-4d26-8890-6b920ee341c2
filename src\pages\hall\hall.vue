<template>
  <view class="game-hall">
    <!-- 头部区域 -->
    <view class="hall-header">
      <view class="logo-section">
        <image class="hall-logo" src="/static/hall-logo.png" mode="aspectFit"></image>
        <text class="hall-title">游戏大厅</text>
        <text class="hall-subtitle">Game Arcade</text>
      </view>
      
      <!-- 用户信息和设置 -->
      <view class="user-section">
        <view class="user-stats">
          <text class="total-score">总分: {{userStats.totalScore}}</text>
          <text class="games-played">游戏次数: {{userStats.gamesPlayed}}</text>
        </view>
        <button class="settings-btn" @click="openSettings">
          <text class="settings-icon">⚙️</text>
        </button>
      </view>
    </view>

    <!-- 游戏列表 -->
    <view class="games-grid">
      <view 
        v-for="game in gameList" 
        :key="game.id"
        class="game-card"
        :class="{ 'locked': !game.unlocked }"
        @click="selectGame(game)"
      >
        <view class="game-preview">
          <image :src="game.thumbnail" class="game-thumbnail" mode="aspectFit"></image>
          <view v-if="!game.unlocked" class="lock-overlay">
            <text class="lock-icon">🔒</text>
          </view>
        </view>
        
        <view class="game-info">
          <text class="game-title">{{game.title}}</text>
          <text class="game-description">{{game.description}}</text>
          
          <view class="game-stats">
            <text class="best-score">最高分: {{game.bestScore || 0}}</text>
            <view class="difficulty-stars">
              <text 
                v-for="star in 5" 
                :key="star"
                class="star"
                :class="{ 'filled': star <= game.difficulty }"
              >★</text>
            </view>
          </view>
          
          <view class="game-actions">
            <button 
              class="play-btn"
              :class="{ 'disabled': !game.unlocked }"
              @click.stop="startGame(game)"
            >
              {{game.unlocked ? '开始游戏' : '未解锁'}}
            </button>
            <button 
              v-if="game.unlocked && game.hasLevels"
              class="levels-btn"
              @click.stop="showLevels(game)"
            >
              选择关卡
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近游戏记录 -->
    <view class="recent-games">
      <text class="section-title">最近游戏</text>
      <scroll-view class="recent-list" scroll-x="true">
        <view 
          v-for="record in recentGames" 
          :key="record.id"
          class="recent-item"
          @click="continueGame(record)"
        >
          <image :src="record.thumbnail" class="recent-thumbnail"></image>
          <view class="recent-info">
            <text class="recent-game">{{record.gameName}}</text>
            <text class="recent-score">分数: {{record.score}}</text>
            <text class="recent-time">{{formatTime(record.timestamp)}}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 设置弹窗 -->
    <view v-if="showSettingsModal" class="settings-modal" @click="closeSettings">
      <view class="settings-content" @click.stop>
        <view class="settings-header">
          <text class="settings-title">游戏设置</text>
          <button class="close-btn" @click="closeSettings">×</button>
        </view>
        
        <view class="settings-list">
          <view class="setting-item">
            <text class="setting-label">音效</text>
            <switch :checked="globalSettings.soundEnabled" @change="toggleSound"></switch>
          </view>
          
          <view class="setting-item">
            <text class="setting-label">音乐</text>
            <switch :checked="globalSettings.musicEnabled" @change="toggleMusic"></switch>
          </view>
          
          <view class="setting-item">
            <text class="setting-label">震动反馈</text>
            <switch :checked="globalSettings.vibrationEnabled" @change="toggleVibration"></switch>
          </view>
          
          <view class="setting-item">
            <text class="setting-label">画质</text>
            <picker 
              :value="globalSettings.quality" 
              :range="qualityOptions"
              @change="changeQuality"
            >
              <view class="picker-text">{{qualityOptions[globalSettings.quality]}}</view>
            </picker>
          </view>
        </view>
        
        <view class="settings-actions">
          <button class="reset-btn" @click="resetSettings">重置设置</button>
          <button class="clear-data-btn" @click="clearGameData">清除数据</button>
        </view>
      </view>
    </view>

    <!-- 关卡选择弹窗 -->
    <view v-if="showLevelsModal" class="levels-modal" @click="closeLevels">
      <view class="levels-content" @click.stop>
        <view class="levels-header">
          <text class="levels-title">{{selectedGame.title}} - 选择关卡</text>
          <button class="close-btn" @click="closeLevels">×</button>
        </view>
        
        <view class="levels-grid">
          <view 
            v-for="level in selectedGame.levels" 
            :key="level.id"
            class="level-card"
            :class="{ 'locked': !level.unlocked, 'completed': level.completed }"
            @click="startLevel(level)"
          >
            <view class="level-number">{{level.id}}</view>
            <text class="level-name">{{level.name}}</text>
            <view class="level-stars">
              <text 
                v-for="star in 3" 
                :key="star"
                class="level-star"
                :class="{ 'earned': star <= (level.stars || 0) }"
              >★</text>
            </view>
            <text class="level-score">{{level.bestScore || 0}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 用户统计
      userStats: {
        totalScore: 0,
        gamesPlayed: 0
      },
      
      // 全局设置
      globalSettings: {
        soundEnabled: true,
        musicEnabled: true,
        vibrationEnabled: true,
        quality: 1 // 0: 低, 1: 中, 2: 高
      },
      
      qualityOptions: ['低画质', '中画质', '高画质'],
      
      // 游戏列表
      gameList: [
        {
          id: 'thunder-fighter',
          title: '雷霆战机',
          description: '经典飞行射击游戏',
          thumbnail: '/static/games/thunder-fighter-thumb.png',
          difficulty: 3,
          unlocked: true,
          hasLevels: true,
          bestScore: 0,
          levels: [
            { id: 1, name: '新手训练', unlocked: true, completed: false, stars: 0, bestScore: 0 },
            { id: 2, name: '星际巡航', unlocked: false, completed: false, stars: 0, bestScore: 0 },
            { id: 3, name: '敌军来袭', unlocked: false, completed: false, stars: 0, bestScore: 0 },
            { id: 4, name: 'Boss战', unlocked: false, completed: false, stars: 0, bestScore: 0 },
            { id: 5, name: '终极挑战', unlocked: false, completed: false, stars: 0, bestScore: 0 }
          ]
        },
        {
          id: 'puzzle-game',
          title: '智力拼图',
          description: '益智类拼图游戏',
          thumbnail: '/static/games/puzzle-thumb.png',
          difficulty: 2,
          unlocked: false,
          hasLevels: true,
          bestScore: 0
        },
        {
          id: 'racing-game',
          title: '极速赛车',
          description: '刺激的赛车竞速',
          thumbnail: '/static/games/racing-thumb.png',
          difficulty: 4,
          unlocked: false,
          hasLevels: false,
          bestScore: 0
        }
      ],
      
      // 最近游戏记录
      recentGames: [],
      
      // 弹窗状态
      showSettingsModal: false,
      showLevelsModal: false,
      selectedGame: null
    }
  },
  
  onLoad() {
    this.loadUserData()
    this.loadGameData()
    this.loadRecentGames()
  },
  
  methods: {
    // 加载用户数据
    loadUserData() {
      const userData = uni.getStorageSync('gameHall_userData') || {}
      this.userStats = {
        totalScore: userData.totalScore || 0,
        gamesPlayed: userData.gamesPlayed || 0
      }
    },
    
    // 加载游戏数据
    loadGameData() {
      const gameData = uni.getStorageSync('gameHall_gameData') || {}
      this.gameList.forEach(game => {
        const savedGame = gameData[game.id]
        if (savedGame) {
          game.bestScore = savedGame.bestScore || 0
          game.unlocked = savedGame.unlocked !== undefined ? savedGame.unlocked : game.unlocked
          if (game.levels && savedGame.levels) {
            game.levels.forEach((level, index) => {
              if (savedGame.levels[index]) {
                Object.assign(level, savedGame.levels[index])
              }
            })
          }
        }
      })
    },
    
    // 加载最近游戏
    loadRecentGames() {
      this.recentGames = uni.getStorageSync('gameHall_recentGames') || []
    },
    
    // 加载全局设置
    loadGlobalSettings() {
      const settings = uni.getStorageSync('gameHall_settings') || {}
      this.globalSettings = {
        soundEnabled: settings.soundEnabled !== undefined ? settings.soundEnabled : true,
        musicEnabled: settings.musicEnabled !== undefined ? settings.musicEnabled : true,
        vibrationEnabled: settings.vibrationEnabled !== undefined ? settings.vibrationEnabled : true,
        quality: settings.quality !== undefined ? settings.quality : 1
      }
    },
    
    // 保存数据
    saveUserData() {
      uni.setStorageSync('gameHall_userData', this.userStats)
    },
    
    saveGameData() {
      const gameData = {}
      this.gameList.forEach(game => {
        gameData[game.id] = {
          bestScore: game.bestScore,
          unlocked: game.unlocked,
          levels: game.levels
        }
      })
      uni.setStorageSync('gameHall_gameData', gameData)
    },
    
    saveGlobalSettings() {
      uni.setStorageSync('gameHall_settings', this.globalSettings)
    },
    
    // 游戏操作
    selectGame(game) {
      if (!game.unlocked) {
        uni.showToast({
          title: '游戏未解锁',
          icon: 'none'
        })
        return
      }
      // 可以在这里添加游戏预览或详情
    },
    
    startGame(game) {
      if (!game.unlocked) return
      
      // 记录游戏启动
      this.addRecentGame(game)
      
      // 跳转到对应游戏页面
      const gameRoutes = {
        'thunder-fighter': '/pages/thunder-fighter/thunder-fighter'
      }
      
      const route = gameRoutes[game.id]
      if (route) {
        uni.navigateTo({
          url: route + '?level=1'
        })
      } else {
        uni.showToast({
          title: '游戏开发中...',
          icon: 'none'
        })
      }
    },
    
    showLevels(game) {
      this.selectedGame = game
      this.showLevelsModal = true
    },
    
    startLevel(level) {
      if (!level.unlocked) {
        uni.showToast({
          title: '关卡未解锁',
          icon: 'none'
        })
        return
      }
      
      this.closeLevels()
      
      // 跳转到游戏并传递关卡信息
      uni.navigateTo({
        url: `/pages/thunder-fighter/thunder-fighter?level=${level.id}`
      })
    },
    
    // 最近游戏
    addRecentGame(game, score = 0) {
      const recentGame = {
        id: Date.now(),
        gameId: game.id,
        gameName: game.title,
        thumbnail: game.thumbnail,
        score: score,
        timestamp: Date.now()
      }
      
      this.recentGames.unshift(recentGame)
      if (this.recentGames.length > 10) {
        this.recentGames = this.recentGames.slice(0, 10)
      }
      
      uni.setStorageSync('gameHall_recentGames', this.recentGames)
    },
    
    continueGame(record) {
      const game = this.gameList.find(g => g.id === record.gameId)
      if (game) {
        this.startGame(game)
      }
    },
    
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'
      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'
      return Math.floor(diff / 86400000) + '天前'
    },
    
    // 设置相关
    openSettings() {
      this.loadGlobalSettings()
      this.showSettingsModal = true
    },
    
    closeSettings() {
      this.showSettingsModal = false
      this.saveGlobalSettings()
    },
    
    closeLevels() {
      this.showLevelsModal = false
      this.selectedGame = null
    },
    
    toggleSound(e) {
      this.globalSettings.soundEnabled = e.detail.value
    },
    
    toggleMusic(e) {
      this.globalSettings.musicEnabled = e.detail.value
    },
    
    toggleVibration(e) {
      this.globalSettings.vibrationEnabled = e.detail.value
    },
    
    changeQuality(e) {
      this.globalSettings.quality = e.detail.value
    },
    
    resetSettings() {
      uni.showModal({
        title: '确认重置',
        content: '确定要重置所有设置吗？',
        success: (res) => {
          if (res.confirm) {
            this.globalSettings = {
              soundEnabled: true,
              musicEnabled: true,
              vibrationEnabled: true,
              quality: 1
            }
            this.saveGlobalSettings()
            uni.showToast({
              title: '设置已重置',
              icon: 'success'
            })
          }
        }
      })
    },
    
    clearGameData() {
      uni.showModal({
        title: '确认清除',
        content: '确定要清除所有游戏数据吗？此操作不可恢复！',
        success: (res) => {
          if (res.confirm) {
            uni.removeStorageSync('gameHall_userData')
            uni.removeStorageSync('gameHall_gameData')
            uni.removeStorageSync('gameHall_recentGames')
            
            // 重新加载数据
            this.loadUserData()
            this.loadGameData()
            this.loadRecentGames()
            
            uni.showToast({
              title: '数据已清除',
              icon: 'success'
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.game-hall {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
}

/* 头部样式 */
.hall-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60rpx;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10px);
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.hall-logo {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.hall-title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.hall-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.user-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 20rpx;
}

.user-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10rpx;
}

.total-score, .games-played {
  color: white;
  font-size: 28rpx;
}

.settings-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-icon {
  font-size: 40rpx;
}

/* 游戏网格样式 */
.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
  gap: 40rpx;
  margin-bottom: 80rpx;
}

.game-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
}

.game-card:active {
  transform: scale(0.98);
}

.game-card.locked {
  opacity: 0.6;
}

.game-preview {
  position: relative;
  height: 200rpx;
  background: #f0f0f0;
}

.game-thumbnail {
  width: 100%;
  height: 100%;
}

.lock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

.lock-icon {
  font-size: 60rpx;
  color: white;
}

.game-info {
  padding: 30rpx;
}

.game-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.game-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.game-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.best-score {
  font-size: 24rpx;
  color: #888;
}

.difficulty-stars {
  display: flex;
  gap: 5rpx;
}

.star {
  font-size: 24rpx;
  color: #ddd;
}

.star.filled {
  color: #ffd700;
}

.game-actions {
  display: flex;
  gap: 20rpx;
}

.play-btn, .levels-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  border: none;
  font-size: 28rpx;
}

.play-btn {
  background: #007AFF;
  color: white;
}

.play-btn.disabled {
  background: #ccc;
  color: #999;
}

.levels-btn {
  background: #34C759;
  color: white;
}

/* 最近游戏样式 */
.recent-games {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 30rpx;
}

.recent-list {
  white-space: nowrap;
}

.recent-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 20rpx;
  vertical-align: top;
}

.recent-thumbnail {
  width: 100%;
  height: 120rpx;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
}

.recent-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.recent-game {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.recent-score, .recent-time {
  font-size: 20rpx;
  color: #666;
}

/* 弹窗样式 */
.settings-modal, .levels-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.settings-content, .levels-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.settings-header, .levels-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.settings-title, .levels-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: none;
  font-size: 36rpx;
  color: #666;
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-bottom: 40rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.setting-label {
  font-size: 32rpx;
  color: #333;
}

.picker-text {
  color: #007AFF;
  font-size: 28rpx;
}

.settings-actions {
  display: flex;
  gap: 20rpx;
}

.reset-btn, .clear-data-btn {
  flex: 1;
  padding: 30rpx;
  border-radius: 10rpx;
  border: none;
  font-size: 28rpx;
}

.reset-btn {
  background: #FF9500;
  color: white;
}

.clear-data-btn {
  background: #FF3B30;
  color: white;
}

/* 关卡选择样式 */
.levels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150rpx, 1fr));
  gap: 20rpx;
}

.level-card {
  background: #f8f8f8;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.level-card:active {
  transform: scale(0.95);
}

.level-card.locked {
  opacity: 0.5;
  background: #e0e0e0;
}

.level-card.completed {
  border-color: #34C759;
  background: #f0fff0;
}

.level-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 10rpx;
}

.level-name {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.level-stars {
  display: flex;
  justify-content: center;
  gap: 5rpx;
  margin-bottom: 10rpx;
}

.level-star {
  font-size: 20rpx;
  color: #ddd;
}

.level-star.earned {
  color: #ffd700;
}

.level-score {
  font-size: 20rpx;
  color: #666;
}
</style>
