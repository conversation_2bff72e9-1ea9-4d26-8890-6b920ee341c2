
## 开发要点



## 文档和资源获取

### MCP服务器配置
始终使用 **context7 MCP server** 搜索AutoGen最新文档和代码规范：
- 优先查询Phaser3.9.0官方文档
- 获取最新的API参考和最佳实践
- 查找代码示例和模式
- 验证版本兼容性和新特性

### 搜索策略
当需要Phaser3.9.0相关信息时：
1. 首先使用context7搜索官方文档


## 注意事项
- 需要VUE3.0
- 所有示例代码使用中文注释
- 遇到问题时优先通过context7搜索最新解决方案

## mcp
"mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp"]
    },
    "taskmaster-ai": {
      "command": "npx",
      "args": ["-y", "--package=task-master-ai", "task-master-ai"],
      "env": {
        "ANTHROPIC_API_KEY": "sk-ant-",
        "OPENAI_API_KEY": "sk-proj-",
        "GOOGLE_API_KEY": "sk-proj-"
      }
    }
},