<template>
  <view class="content">
    <div id="phaser-game" class="game-container"></div>
  </view>
</template>

<script>
import Phaser from 'phaser'

export default {
  data() {
    return {
      game: null,
      player: null,
      bullets: null,
      enemies: null,
      boss: null,
      lastFired: 0,
      score: 0,
      scoreText: null,
      lives: 3,
      livesText: null,
      background: null,
      powerUps: null,
      fireSound: null,
      explosionSound: null,
      powerLevel: 1,
      wingmen: null,
      bombs: 3,
      bombText: null,
    }
  },
  mounted() {
    const config = {
      type: Phaser.AUTO,
      width: window.innerWidth,
      height: window.innerHeight,
      parent: 'phaser-game',
      backgroundColor: '#000000',
      scene: {
        preload: function() {
          // 使用 /static/ 前缀来正确引用静态资源
          this.load.image('player', '/static/PNG/playerShip1_blue.png')
          this.load.image('wingman', '/static/PNG/playerShip2_orange.png')
          this.load.image('bullet', '/static/PNG/Lasers/laserBlue01.png')
          this.load.image('enemy', '/static/PNG/Enemies/enemyBlack1.png')
          this.load.image('boss', '/static/PNG/Enemies/enemyRed5.png')
          this.load.image('background', '/static/PNG/Backgrounds/darkPurple.png')
          this.load.image('powerupBolt', '/static/PNG/Power-ups/powerupBlue_bolt.png')
          this.load.image('powerupStar', '/static/PNG/Power-ups/powerupBlue_star.png')
          // 加载爆炸动画帧
          for (let i = 0; i < 20; i++) {
            const frameNum = i.toString().padStart(2, '0')
            this.load.image(`fire${frameNum}`, `/static/PNG/Effects/fire${frameNum}.png`)
          }
          this.load.audio('fire_sound', '/static/Bonus/sfx_laser1.ogg')
          this.load.audio('explosion_sound', '/static/Bonus/sfx_twoTone.ogg')
          this.load.audio('bomb_sound', '/static/Bonus/sfx_lose.ogg')
        },
        create: function() {
          const vueComponent = this.game.vueComponent

          vueComponent.background = this.add.tileSprite(0, 0, window.innerWidth, window.innerHeight, 'background').setOrigin(0, 0)

          vueComponent.player = this.physics.add.sprite(window.innerWidth / 2, window.innerHeight - 100, 'player').setScale(0.5)
          vueComponent.player.setCollideWorldBounds(true)

          vueComponent.wingmen = this.physics.add.group()

          vueComponent.bullets = this.physics.add.group({ defaultKey: 'bullet', maxSize: 100 })
          vueComponent.enemies = this.physics.add.group({ defaultKey: 'enemy', maxSize: 20 })
          vueComponent.powerUps = this.physics.add.group()

          this.input.on('pointermove', (pointer) => { vueComponent.player.x = pointer.x })
          this.input.on('pointerdown', (pointer) => { vueComponent.player.x = pointer.x })

          this.time.addEvent({ delay: 1000, callback: () => vueComponent.spawnEnemy(), callbackScope: this, loop: true })

          this.physics.add.collider(vueComponent.bullets, vueComponent.enemies, (bullet, enemy) => vueComponent.hitEnemy(bullet, enemy), null, this)
          this.physics.add.collider(vueComponent.player, vueComponent.enemies, (player, enemy) => vueComponent.playerHit(player, enemy), null, this)
          this.physics.add.overlap(vueComponent.player, vueComponent.powerUps, (player, powerUp) => vueComponent.collectPowerUp(player, powerUp), null, this)

          vueComponent.scoreText = this.add.text(16, 16, 'Score: 0', { fontSize: '32px', fill: '#FFF' })
          vueComponent.livesText = this.add.text(window.innerWidth - 150, 16, 'Lives: 3', { fontSize: '32px', fill: '#FFF' })
          vueComponent.bombText = this.add.text(16, 50, 'Bombs: 3', { fontSize: '32px', fill: '#FFF' })

          const bombButton = this.add.text(window.innerWidth - 150, 50, 'BOMB', { fontSize: '32px', fill: '#FF0', backgroundColor: '#555' }).setPadding(10).setInteractive()
          bombButton.on('pointerdown', () => vueComponent.useBomb(), this)

          // 创建爆炸动画
          const frames = []
          for (let i = 0; i < 20; i++) {
            const frameNum = i.toString().padStart(2, '0')
            frames.push({ key: `fire${frameNum}` })
          }
          this.anims.create({
            key: 'explode',
            frames: frames,
            frameRate: 20,
            repeat: 0,
            hideOnComplete: true
          })

          vueComponent.fireSound = this.sound.add('fire_sound')
          vueComponent.explosionSound = this.sound.add('explosion_sound')
          vueComponent.bombSound = this.sound.add('bomb_sound')

          // 存储场景引用到 Vue 组件中
          vueComponent.scene = this
        },
        update: function(time, delta) { this.game.vueComponent.update.call(this, time, delta) },
      },
      physics: {
        default: 'arcade',
        arcade: {
          gravity: { y: 0 },
        },
      },
    }
    this.game = new Phaser.Game(config)
    this.game.vueComponent = this // Store Vue component instance
  },
  beforeUnmount() {
    if (this.game) {
      this.game.destroy(true)
    }
  },
  methods: {


    update(time) {
      this.background.tilePositionY -= 0.5

      if (this.player.active && time > this.lastFired) {
        this.fireBullet(time)
      }

      this.bullets.children.each((b) => { if (b.active && b.y < 0) { b.setActive(false).setVisible(false) } })
      this.enemies.children.each((e) => { if (e.active && e.y > window.innerHeight) { e.setActive(false).setVisible(false) } })

      if (this.score >= 200 && !this.boss) {
        this.spawnBoss()
      }

      Phaser.Actions.SetXY(this.wingmen.getChildren(), this.player.x - 50, this.player.y + 20, 100)
    },
    fireBullet(time) {
      if (this.powerLevel >= 1) {
        const bullet = this.bullets.get(this.player.x, this.player.y - 50)
        if (bullet) bullet.setActive(true).setVisible(true).body.velocity.y = -400
      }
      if (this.powerLevel >= 2) {
        const bullet1 = this.bullets.get(this.player.x - 20, this.player.y - 30)
        const bullet2 = this.bullets.get(this.player.x + 20, this.player.y - 30)
        if (bullet1) bullet1.setActive(true).setVisible(true).body.velocity.y = -400
        if (bullet2) bullet2.setActive(true).setVisible(true).body.velocity.y = -400
      }
      this.wingmen.getChildren().forEach(wingman => {
        const bullet = this.bullets.get(wingman.x, wingman.y)
        if (bullet) bullet.setActive(true).setVisible(true).body.velocity.y = -400
      })

      this.lastFired = time + 150
      this.fireSound.play({ volume: 0.5 })
    },
    spawnEnemy() {
      if (this.boss) return
      const enemy = this.enemies.get(Phaser.Math.Between(50, window.innerWidth - 50), -50)
      if (enemy) {
        enemy.setActive(true).setVisible(true).body.velocity.y = Phaser.Math.Between(100, 200)
      }
    },
    spawnBoss() {
      this.boss = this.scene.physics.add.sprite(window.innerWidth / 2, -200, 'boss').setScale(1.5)
      this.boss.health = 200
      this.scene.physics.add.collider(this.bullets, this.boss, (bullet, boss) => this.hitBoss(boss, bullet), null, this.scene)
      this.scene.tweens.add({ targets: this.boss, y: 150, duration: 3000, ease: 'Power2' })
    },
    hitEnemy(bullet, enemy) {
      bullet.setActive(false).setVisible(false)
      this.explode(enemy.x, enemy.y)
      enemy.setActive(false).setVisible(false)
      this.score += 10
      this.scoreText.setText('Score: ' + this.score)

      const rand = Phaser.Math.Between(0, 10)
      if (rand > 8) {
        const powerUp = this.powerUps.create(enemy.x, enemy.y, 'powerupBolt')
        powerUp.type = 'bolt'
        powerUp.setVelocityY(100)
      } else if (rand > 6) {
        const powerUp = this.powerUps.create(enemy.x, enemy.y, 'powerupStar')
        powerUp.type = 'star'
        powerUp.setVelocityY(100)
      }
    },
    hitBoss(boss, bullet) {
      bullet.setActive(false).setVisible(false)
      boss.health -= 1
      this.explode(bullet.x, bullet.y)
      if (boss.health <= 0) {
        this.explode(boss.x, boss.y)
        boss.destroy()
        this.boss = null
        this.score += 1000
        this.scoreText.setText('Score: ' + this.score)
      }
    },
    playerHit(player, enemy) {
      this.explode(player.x, player.y)
      enemy.setActive(false).setVisible(false)
      this.lives -= 1
      this.livesText.setText('Lives: ' + this.lives)
      this.powerLevel = 1
      this.wingmen.clear(true, true)

      if (this.lives === 0) {
        this.scene.physics.pause()
        player.setTint(0xff0000)
        this.scene.add.text(window.innerWidth/2, window.innerHeight/2 - 50, 'GAME OVER', { fontSize: '64px', fill: '#ff0000' }).setOrigin(0.5)
        this.scene.add.text(window.innerWidth/2, window.innerHeight/2 + 20, 'Score: ' + this.score, { fontSize: '48px', fill: '#FFF' }).setOrigin(0.5)
        const restartButton = this.scene.add.text(window.innerWidth/2, window.innerHeight/2 + 100, 'RESTART', { fontSize: '32px', fill: '#0F0', backgroundColor: '#555' }).setPadding(10).setInteractive().setOrigin(0.5)
        restartButton.on('pointerdown', () => this.restartGame(), this.scene)
      }
    },
    restartGame() {
      this.score = 0
      this.lives = 3
      this.powerLevel = 1
      this.bombs = 3
      this.boss = null

      this.scoreText.setText('Score: ' + this.score)
      this.livesText.setText('Lives: ' + this.lives)
      this.bombText.setText('Bombs: ' + this.bombs)

      this.bullets.clear(true, true)
      this.enemies.clear(true, true)
      this.powerUps.clear(true, true)
      this.wingmen.clear(true, true)

      this.scene.scene.restart()
    },
    collectPowerUp(player, powerUp) {
      powerUp.disableBody(true, true)
      if (powerUp.type === 'bolt') {
        if (this.powerLevel < 2) this.powerLevel++
      } else if (powerUp.type === 'star') {
        if (this.wingmen.getChildren().length < 2) {
          this.wingmen.create(player.x - 50, player.y + 20, 'wingman').setScale(0.4)
        }
      }
    },
    useBomb() {
      if (this.bombs > 0) {
        this.enemies.children.each(e => { if(e.active) this.explode(e.x, e.y); e.setActive(false).setVisible(false); })
        if (this.boss) {
          this.boss.health -= 50
          if (this.boss.health <= 0) {
             this.explode(this.boss.x, this.boss.y)
             this.boss.destroy()
             this.boss = null
          }
        }
        this.bombs--
        this.bombText.setText('Bombs: ' + this.bombs)
        this.bombSound.play()
      }
    },
    explode(x, y) {
      const explosion = this.scene.add.sprite(x, y, 'explosion').play('explode')
      this.explosionSound.play()
      explosion.on('animationcomplete', () => { explosion.destroy() })
    },
  },
}
</script>

<style>
.content,
.game-container {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
</style>
