<template>
  <view class="content">
    <!-- H5版本 -->
    <div v-if="isH5" id="phaser-game" class="game-container"></div>

    <!-- 小程序版本选择页面 -->
    <view v-else class="selection-page">
      <view class="title">雷霆战机</view>
      <view class="subtitle">Thunder Fighter</view>

      <view class="game-preview">
        <image class="preview-image" src="/static/logo.png" mode="aspectFit"></image>
      </view>

      <view class="version-info">
        <text class="info-text">检测到您在微信小程序中运行</text>
        <text class="info-text">为了更好的游戏体验，我们为您准备了专门的小程序版本</text>
      </view>

      <view class="button-group">
        <button class="start-btn" @click="startMiniProgramGame">开始游戏</button>
        <button class="info-btn" @click="showGameInfo">游戏说明</button>
      </view>

      <view class="features">
        <view class="feature-item">
          <text class="feature-icon">🚀</text>
          <text class="feature-text">流畅的触控操作</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">💥</text>
          <text class="feature-text">精彩的战斗效果</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">🎯</text>
          <text class="feature-text">多种道具系统</text>
        </view>
      </view>
    </view>

    <!-- 游戏说明弹窗 -->
    <view v-if="showInfo" class="info-modal" @click="hideGameInfo">
      <view class="info-content" @click.stop>
        <view class="info-title">游戏说明</view>
        <view class="info-list">
          <text class="info-item">• 滑动屏幕控制飞机移动</text>
          <text class="info-item">• 飞机会自动射击</text>
          <text class="info-item">• 击败敌机获得分数</text>
          <text class="info-item">• 收集道具提升火力</text>
          <text class="info-item">• 使用炸弹清除所有敌机</text>
          <text class="info-item">• 避免与敌机碰撞</text>
        </view>
        <button class="close-btn" @click="hideGameInfo">知道了</button>
      </view>
    </view>
  </view>
</template>

<script>
import Phaser from 'phaser'

export default {
  data() {
    return {
      // 平台检测
      isH5: false,
      showInfo: false,

      // Phaser 游戏相关（仅H5使用）
      game: null,
      player: null,
      bullets: null,
      enemies: null,
      boss: null,
      lastFired: 0,
      score: 0,
      scoreText: null,
      lives: 3,
      livesText: null,
      background: null,
      powerUps: null,
      fireSound: null,
      explosionSound: null,
      powerLevel: 1,
      wingmen: null,
      bombs: 3,
      bombText: null,
      scene: null,
      audioEnabled: false,
      audioHintText: null,
    }
  },
  mounted() {
    // 检测运行平台
    // #ifdef H5
    this.isH5 = true
    this.initPhaserGame()
    // #endif

    // #ifdef MP-WEIXIN
    this.isH5 = false
    // #endif
  },

  beforeUnmount() {
    if (this.game) {
      this.game.destroy(true)
    }
  },

  methods: {
    // 启动小程序游戏
    startMiniProgramGame() {
      uni.navigateTo({
        url: '/pages/game/game'
      })
    },

    // 显示游戏说明
    showGameInfo() {
      this.showInfo = true
    },

    // 隐藏游戏说明
    hideGameInfo() {
      this.showInfo = false
    },

    // 初始化 Phaser 游戏（仅H5）
    initPhaserGame() {
      const config = {
      type: Phaser.AUTO,
      width: window.innerWidth,
      height: window.innerHeight,
      parent: 'phaser-game',
      backgroundColor: '#000000',
      scene: {
        preload: function() {
          // 使用 /static/ 前缀来正确引用静态资源
          this.load.image('player', '/static/PNG/playerShip1_blue.png')
          this.load.image('wingman', '/static/PNG/playerShip2_orange.png')
          this.load.image('bullet', '/static/PNG/Lasers/laserBlue01.png')
          this.load.image('enemy', '/static/PNG/Enemies/enemyBlack1.png')
          this.load.image('boss', '/static/PNG/Enemies/enemyRed5.png')
          this.load.image('background', '/static/PNG/Backgrounds/darkPurple.png')
          this.load.image('powerupBolt', '/static/PNG/Power-ups/powerupBlue_bolt.png')
          this.load.image('powerupStar', '/static/PNG/Power-ups/powerupBlue_star.png')
          // 加载爆炸动画帧
          for (let i = 0; i < 20; i++) {
            const frameNum = i.toString().padStart(2, '0')
            this.load.image(`fire${frameNum}`, `/static/PNG/Effects/fire${frameNum}.png`)
          }
          this.load.audio('fire_sound', '/static/Bonus/sfx_laser1.ogg')
          this.load.audio('explosion_sound', '/static/Bonus/sfx_twoTone.ogg')
          this.load.audio('bomb_sound', '/static/Bonus/sfx_lose.ogg')
        },
        create: function() {
          const vueComponent = this.game.vueComponent

          vueComponent.background = this.add.tileSprite(0, 0, window.innerWidth, window.innerHeight, 'background').setOrigin(0, 0)

          vueComponent.player = this.physics.add.sprite(window.innerWidth / 2, window.innerHeight - 100, 'player').setScale(0.5)
          vueComponent.player.setCollideWorldBounds(true)

          vueComponent.wingmen = this.physics.add.group()

          vueComponent.bullets = this.physics.add.group({ defaultKey: 'bullet', maxSize: 100 })
          vueComponent.enemies = this.physics.add.group({ defaultKey: 'enemy', maxSize: 20 })
          vueComponent.powerUps = this.physics.add.group()

          this.input.on('pointermove', (pointer) => { vueComponent.player.x = pointer.x })
          this.input.on('pointerdown', (pointer) => {
            vueComponent.player.x = pointer.x
            // 启用音频（用户交互后）
            if (!vueComponent.audioEnabled) {
              vueComponent.enableAudio()
            }
          })

          this.time.addEvent({ delay: 1000, callback: () => vueComponent.spawnEnemy(), callbackScope: this, loop: true })

          this.physics.add.collider(vueComponent.bullets, vueComponent.enemies, (bullet, enemy) => vueComponent.hitEnemy(bullet, enemy), null, this)
          this.physics.add.collider(vueComponent.player, vueComponent.enemies, (player, enemy) => vueComponent.playerHit(player, enemy), null, this)
          this.physics.add.overlap(vueComponent.player, vueComponent.powerUps, (player, powerUp) => vueComponent.collectPowerUp(player, powerUp), null, this)

          vueComponent.scoreText = this.add.text(16, 16, 'Score: 0', { fontSize: '32px', fill: '#FFF' })
          vueComponent.livesText = this.add.text(window.innerWidth - 150, 16, 'Lives: 3', { fontSize: '32px', fill: '#FFF' })
          vueComponent.bombText = this.add.text(16, 50, 'Bombs: 3', { fontSize: '32px', fill: '#FFF' })

          // 音频提示文本
          vueComponent.audioHintText = this.add.text(window.innerWidth/2, window.innerHeight/2 + 200, 'Click to enable sound', {
            fontSize: '24px',
            fill: '#FFD700',
            backgroundColor: '#000000',
            padding: { x: 10, y: 5 }
          }).setOrigin(0.5)

          const bombButton = this.add.text(window.innerWidth - 150, 50, 'BOMB', { fontSize: '32px', fill: '#FF0', backgroundColor: '#555' }).setPadding(10).setInteractive()
          bombButton.on('pointerdown', () => vueComponent.useBomb(), this)

          // 创建爆炸动画
          const frames = []
          for (let i = 0; i < 20; i++) {
            const frameNum = i.toString().padStart(2, '0')
            frames.push({ key: `fire${frameNum}` })
          }
          this.anims.create({
            key: 'explode',
            frames: frames,
            frameRate: 20,
            repeat: 0,
            hideOnComplete: true
          })

          vueComponent.fireSound = this.sound.add('fire_sound')
          vueComponent.explosionSound = this.sound.add('explosion_sound')
          vueComponent.bombSound = this.sound.add('bomb_sound')

          // 存储场景引用到 Vue 组件中
          vueComponent.scene = this
        },
        update: function(time, delta) { this.game.vueComponent.update.call(this, time, delta) },
      },
      physics: {
        default: 'arcade',
        arcade: {
          gravity: { y: 0 },
        },
      },
      }
      this.game = new Phaser.Game(config)
      this.game.vueComponent = this // Store Vue component instance
    },
    enableAudio() {
      if (!this.audioEnabled && this.scene && this.scene.sound) {
        // 恢复音频上下文
        if (this.scene.sound.context && this.scene.sound.context.state === 'suspended') {
          this.scene.sound.context.resume()
        }
        this.audioEnabled = true
        // 隐藏音频提示文本
        if (this.audioHintText) {
          this.audioHintText.setVisible(false)
        }
        console.log('Audio enabled after user interaction')
      }
    },


    update(time) {
      this.background.tilePositionY -= 0.5

      if (this.player.active && time > this.lastFired) {
        this.fireBullet(time)
      }

      this.bullets.children.each((b) => { if (b.active && b.y < 0) { b.setActive(false).setVisible(false) } })
      this.enemies.children.each((e) => { if (e.active && e.y > window.innerHeight) { e.setActive(false).setVisible(false) } })

      if (this.score >= 200 && !this.boss) {
        this.spawnBoss()
      }

      Phaser.Actions.SetXY(this.wingmen.getChildren(), this.player.x - 50, this.player.y + 20, 100)
    },
    fireBullet(time) {
      if (this.powerLevel >= 1) {
        const bullet = this.bullets.get(this.player.x, this.player.y - 50)
        if (bullet) bullet.setActive(true).setVisible(true).body.velocity.y = -400
      }
      if (this.powerLevel >= 2) {
        const bullet1 = this.bullets.get(this.player.x - 20, this.player.y - 30)
        const bullet2 = this.bullets.get(this.player.x + 20, this.player.y - 30)
        if (bullet1) bullet1.setActive(true).setVisible(true).body.velocity.y = -400
        if (bullet2) bullet2.setActive(true).setVisible(true).body.velocity.y = -400
      }
      this.wingmen.getChildren().forEach(wingman => {
        const bullet = this.bullets.get(wingman.x, wingman.y)
        if (bullet) bullet.setActive(true).setVisible(true).body.velocity.y = -400
      })

      this.lastFired = time + 150
      // 安全播放音效
      if (this.audioEnabled && this.fireSound) {
        try {
          this.fireSound.play({ volume: 0.5 })
        } catch (error) {
          console.warn('Failed to play fire sound:', error)
        }
      }
    },
    spawnEnemy() {
      if (this.boss) return
      const enemy = this.enemies.get(Phaser.Math.Between(50, window.innerWidth - 50), -50)
      if (enemy) {
        enemy.setActive(true).setVisible(true).body.velocity.y = Phaser.Math.Between(100, 200)
      }
    },
    spawnBoss() {
      this.boss = this.scene.physics.add.sprite(window.innerWidth / 2, -200, 'boss').setScale(1.5)
      this.boss.health = 200
      this.scene.physics.add.collider(this.bullets, this.boss, (bullet, boss) => this.hitBoss(boss, bullet), null, this.scene)
      this.scene.tweens.add({ targets: this.boss, y: 150, duration: 3000, ease: 'Power2' })
    },
    hitEnemy(bullet, enemy) {
      bullet.setActive(false).setVisible(false)
      this.explode(enemy.x, enemy.y)
      enemy.setActive(false).setVisible(false)
      this.score += 10
      this.scoreText.setText('Score: ' + this.score)

      const rand = Phaser.Math.Between(0, 10)
      if (rand > 8) {
        const powerUp = this.powerUps.create(enemy.x, enemy.y, 'powerupBolt')
        powerUp.type = 'bolt'
        powerUp.setVelocityY(100)
      } else if (rand > 6) {
        const powerUp = this.powerUps.create(enemy.x, enemy.y, 'powerupStar')
        powerUp.type = 'star'
        powerUp.setVelocityY(100)
      }
    },
    hitBoss(boss, bullet) {
      bullet.setActive(false).setVisible(false)
      boss.health -= 1
      this.explode(bullet.x, bullet.y)
      if (boss.health <= 0) {
        this.explode(boss.x, boss.y)
        boss.destroy()
        this.boss = null
        this.score += 1000
        this.scoreText.setText('Score: ' + this.score)
      }
    },
    playerHit(player, enemy) {
      this.explode(player.x, player.y)
      enemy.setActive(false).setVisible(false)
      this.lives -= 1
      this.livesText.setText('Lives: ' + this.lives)
      this.powerLevel = 1
      this.wingmen.clear(true, true)

      if (this.lives === 0) {
        this.scene.physics.pause()
        player.setTint(0xff0000)
        this.scene.add.text(window.innerWidth/2, window.innerHeight/2 - 50, 'GAME OVER', { fontSize: '64px', fill: '#ff0000' }).setOrigin(0.5)
        this.scene.add.text(window.innerWidth/2, window.innerHeight/2 + 20, 'Score: ' + this.score, { fontSize: '48px', fill: '#FFF' }).setOrigin(0.5)
        const restartButton = this.scene.add.text(window.innerWidth/2, window.innerHeight/2 + 100, 'RESTART', { fontSize: '32px', fill: '#0F0', backgroundColor: '#555' }).setPadding(10).setInteractive().setOrigin(0.5)
        restartButton.on('pointerdown', () => this.restartGame(), this.scene)
      }
    },
    restartGame() {
      this.score = 0
      this.lives = 3
      this.powerLevel = 1
      this.bombs = 3
      this.boss = null

      this.scoreText.setText('Score: ' + this.score)
      this.livesText.setText('Lives: ' + this.lives)
      this.bombText.setText('Bombs: ' + this.bombs)

      this.bullets.clear(true, true)
      this.enemies.clear(true, true)
      this.powerUps.clear(true, true)
      this.wingmen.clear(true, true)

      this.scene.scene.restart()
    },
    collectPowerUp(player, powerUp) {
      powerUp.disableBody(true, true)
      if (powerUp.type === 'bolt') {
        if (this.powerLevel < 2) this.powerLevel++
      } else if (powerUp.type === 'star') {
        if (this.wingmen.getChildren().length < 2) {
          this.wingmen.create(player.x - 50, player.y + 20, 'wingman').setScale(0.4)
        }
      }
    },
    useBomb() {
      if (this.bombs > 0) {
        this.enemies.children.each(e => { if(e.active) this.explode(e.x, e.y); e.setActive(false).setVisible(false); })
        if (this.boss) {
          this.boss.health -= 50
          if (this.boss.health <= 0) {
             this.explode(this.boss.x, this.boss.y)
             this.boss.destroy()
             this.boss = null
          }
        }
        this.bombs--
        this.bombText.setText('Bombs: ' + this.bombs)
        // 安全播放炸弹音效
        if (this.audioEnabled && this.bombSound) {
          try {
            this.bombSound.play()
          } catch (error) {
            console.warn('Failed to play bomb sound:', error)
          }
        }
      }
    },
    explode(x, y) {
      const explosion = this.scene.add.sprite(x, y, 'fire00').play('explode')
      // 安全播放爆炸音效
      if (this.audioEnabled && this.explosionSound) {
        try {
          this.explosionSound.play()
        } catch (error) {
          console.warn('Failed to play explosion sound:', error)
        }
      }
      explosion.on('animationcomplete', () => { explosion.destroy() })
    },

    // 启动小程序游戏
    startMiniProgramGame() {
      uni.navigateTo({
        url: '/pages/game/game'
      })
    },

    // 显示游戏说明
    showGameInfo() {
      this.showInfo = true
    },

    // 隐藏游戏说明
    hideGameInfo() {
      this.showInfo = false
    },
  },
}
</script>

<style>
.content,
.game-container {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 小程序选择页面样式 */
.selection-page {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  box-sizing: border-box;
}

.title {
  font-size: 80rpx;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 36rpx;
  color: rgba(255,255,255,0.8);
  margin-bottom: 80rpx;
}

.game-preview {
  width: 200rpx;
  height: 200rpx;
  background: rgba(255,255,255,0.1);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;
}

.preview-image {
  width: 120rpx;
  height: 120rpx;
}

.version-info {
  text-align: center;
  margin-bottom: 80rpx;
}

.info-text {
  display: block;
  color: rgba(255,255,255,0.9);
  font-size: 28rpx;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-bottom: 80rpx;
}

.start-btn, .info-btn {
  padding: 30rpx 80rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  border: none;
  min-width: 300rpx;
}

.start-btn {
  background: #ff6b6b;
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(255,107,107,0.3);
}

.info-btn {
  background: rgba(255,255,255,0.2);
  color: white;
  border: 2rpx solid rgba(255,255,255,0.3);
}

.features {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.feature-icon {
  font-size: 40rpx;
}

.feature-text {
  color: rgba(255,255,255,0.9);
  font-size: 28rpx;
}

/* 游戏说明弹窗 */
.info-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.info-content {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx;
  margin: 40rpx;
  max-width: 600rpx;
}

.info-title {
  font-size: 48rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 60rpx;
}

.info-item {
  font-size: 32rpx;
  color: #666;
  line-height: 1.5;
}

.close-btn {
  width: 100%;
  padding: 30rpx;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 32rpx;
}
</style>
