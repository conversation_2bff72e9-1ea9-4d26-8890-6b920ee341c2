// 雷霆战机关卡配置
export const levelConfigs = {
  1: {
    id: 1,
    name: '新手训练',
    description: '熟悉游戏操作，击败基础敌机',
    objectives: [
      '击败 20 个敌机',
      '收集 3 个道具',
      '生存 60 秒'
    ],
    lives: 5,
    bombs: 5,
    duration: 60000, // 60秒
    enemyTypes: ['basic'],
    enemySpawnRate: 2000, // 2秒一个敌机
    enemySpeed: 2,
    enemyHealth: 1,
    powerUpRate: 0.3, // 30%概率掉落道具
    targetScore: 1000,
    targetEnemies: 20,
    starThresholds: [500, 800, 1000], // 1星、2星、3星分数要求
    backgroundMusic: 'level1_bg',
    hasBoss: false
  },
  
  2: {
    id: 2,
    name: '星际巡航',
    description: '在星际空间中战斗，敌机更加强大',
    objectives: [
      '击败 35 个敌机',
      '达到 3000 分',
      '使用炸弹不超过 2 次'
    ],
    lives: 4,
    bombs: 3,
    duration: 90000, // 90秒
    enemyTypes: ['basic', 'fast'],
    enemySpawnRate: 1500, // 1.5秒一个敌机
    enemySpeed: 3,
    enemyHealth: 2,
    powerUpRate: 0.25,
    targetScore: 3000,
    targetEnemies: 35,
    starThresholds: [2000, 2500, 3000],
    backgroundMusic: 'level2_bg',
    hasBoss: false
  },
  
  3: {
    id: 3,
    name: '敌军来袭',
    description: '大批敌军来袭，准备迎接激烈战斗',
    objectives: [
      '击败 50 个敌机',
      '达到 5000 分',
      '生存 120 秒'
    ],
    lives: 3,
    bombs: 3,
    duration: 120000, // 120秒
    enemyTypes: ['basic', 'fast', 'heavy'],
    enemySpawnRate: 1000, // 1秒一个敌机
    enemySpeed: 4,
    enemyHealth: 3,
    powerUpRate: 0.2,
    targetScore: 5000,
    targetEnemies: 50,
    starThresholds: [3500, 4200, 5000],
    backgroundMusic: 'level3_bg',
    hasBoss: false
  },
  
  4: {
    id: 4,
    name: 'Boss战',
    description: '面对强大的Boss，这将是真正的考验',
    objectives: [
      '击败Boss',
      '达到 8000 分',
      '剩余生命不少于 1 个'
    ],
    lives: 3,
    bombs: 2,
    duration: 180000, // 180秒
    enemyTypes: ['basic', 'fast', 'heavy'],
    enemySpawnRate: 800,
    enemySpeed: 5,
    enemyHealth: 4,
    powerUpRate: 0.15,
    targetScore: 8000,
    targetEnemies: 30, // 普通敌机数量减少，主要是Boss战
    starThresholds: [6000, 7000, 8000],
    backgroundMusic: 'boss_bg',
    hasBoss: true,
    bossConfig: {
      health: 50,
      speed: 2,
      fireRate: 500,
      patterns: ['straight', 'spread', 'circle'],
      phases: 3 // Boss有3个阶段
    }
  },
  
  5: {
    id: 5,
    name: '终极挑战',
    description: '最终挑战，只有真正的飞行员才能通过',
    objectives: [
      '击败 100 个敌机',
      '击败终极Boss',
      '达到 15000 分'
    ],
    lives: 3,
    bombs: 2,
    duration: 300000, // 300秒
    enemyTypes: ['basic', 'fast', 'heavy', 'elite'],
    enemySpawnRate: 600,
    enemySpeed: 6,
    enemyHealth: 5,
    powerUpRate: 0.1,
    targetScore: 15000,
    targetEnemies: 100,
    starThresholds: [12000, 13500, 15000],
    backgroundMusic: 'final_bg',
    hasBoss: true,
    bossConfig: {
      health: 100,
      speed: 3,
      fireRate: 300,
      patterns: ['straight', 'spread', 'circle', 'laser', 'missile'],
      phases: 5 // 终极Boss有5个阶段
    }
  }
}

// 敌机类型配置
export const enemyTypeConfigs = {
  basic: {
    name: '基础敌机',
    health: 1,
    speed: 2,
    score: 100,
    sprite: 'enemy_basic',
    fireRate: 0, // 不开火
    size: { width: 40, height: 40 }
  },
  
  fast: {
    name: '快速敌机',
    health: 1,
    speed: 4,
    score: 150,
    sprite: 'enemy_fast',
    fireRate: 0,
    size: { width: 35, height: 35 }
  },
  
  heavy: {
    name: '重型敌机',
    health: 3,
    speed: 1.5,
    score: 300,
    sprite: 'enemy_heavy',
    fireRate: 2000, // 2秒开火一次
    size: { width: 60, height: 60 }
  },
  
  elite: {
    name: '精英敌机',
    health: 5,
    speed: 3,
    score: 500,
    sprite: 'enemy_elite',
    fireRate: 1500, // 1.5秒开火一次
    size: { width: 50, height: 50 }
  }
}

// 道具类型配置
export const powerUpConfigs = {
  power: {
    name: '火力提升',
    sprite: 'powerup_power',
    effect: 'increasePower',
    duration: 10000, // 10秒
    size: { width: 30, height: 30 }
  },
  
  speed: {
    name: '速度提升',
    sprite: 'powerup_speed',
    effect: 'increaseSpeed',
    duration: 8000, // 8秒
    size: { width: 30, height: 30 }
  },
  
  shield: {
    name: '护盾',
    sprite: 'powerup_shield',
    effect: 'addShield',
    duration: 15000, // 15秒
    size: { width: 30, height: 30 }
  },
  
  bomb: {
    name: '炸弹',
    sprite: 'powerup_bomb',
    effect: 'addBomb',
    duration: 0, // 立即生效
    size: { width: 30, height: 30 }
  },
  
  life: {
    name: '生命',
    sprite: 'powerup_life',
    effect: 'addLife',
    duration: 0, // 立即生效
    size: { width: 30, height: 30 }
  }
}

// 获取关卡配置
export function getLevelConfig(levelId) {
  return levelConfigs[levelId] || levelConfigs[1]
}

// 获取敌机类型配置
export function getEnemyTypeConfig(type) {
  return enemyTypeConfigs[type] || enemyTypeConfigs.basic
}

// 获取道具配置
export function getPowerUpConfig(type) {
  return powerUpConfigs[type] || powerUpConfigs.power
}

// 计算星级评价
export function calculateStars(score, levelId) {
  const config = getLevelConfig(levelId)
  const thresholds = config.starThresholds
  
  if (score >= thresholds[2]) return 3
  if (score >= thresholds[1]) return 2
  if (score >= thresholds[0]) return 1
  return 0
}

// 检查关卡完成条件
export function checkLevelComplete(gameState, levelId) {
  const config = getLevelConfig(levelId)
  const { score, enemiesKilled, timeElapsed, livesRemaining } = gameState
  
  // 基本完成条件
  let completed = true
  let objectives = []
  
  // 检查分数目标
  if (score >= config.targetScore) {
    objectives.push('score')
  } else {
    completed = false
  }
  
  // 检查敌机击败数量
  if (enemiesKilled >= config.targetEnemies) {
    objectives.push('enemies')
  } else {
    completed = false
  }
  
  // 检查生存时间（如果有要求）
  if (config.duration && timeElapsed >= config.duration) {
    objectives.push('survival')
  }
  
  // 检查Boss战（如果有Boss）
  if (config.hasBoss && gameState.bossDefeated) {
    objectives.push('boss')
  } else if (config.hasBoss) {
    completed = false
  }
  
  return {
    completed,
    objectives,
    stars: completed ? calculateStars(score, levelId) : 0
  }
}
