<template>
  <view class="thunder-fighter">
    <!-- H5版本 -->
    <div v-if="isH5" id="phaser-game" class="game-container"></div>

    <!-- 小程序版本 -->
    <view v-else class="miniprogram-game">
      <!-- 游戏画布 -->
      <canvas
        canvas-id="gameCanvas"
        class="game-canvas"
        @touchstart="onTouchStart"
        @touchmove="onTouchMove"
        @touchend="onTouchEnd"
      ></canvas>

      <!-- 游戏UI -->
      <view class="game-ui">
        <!-- 顶部信息栏 -->
        <view class="top-bar">
          <button class="back-btn" @click="goBack">
            <text class="back-icon">←</text>
          </button>

          <view class="game-info">
            <text class="level-text">关卡 {{currentLevel}}</text>
            <text class="score-text">分数: {{score}}</text>
          </view>

          <view class="status-info">
            <text class="lives-text">生命: {{lives}}</text>
            <text class="bombs-text">炸弹: {{bombs}}</text>
          </view>
        </view>

        <!-- 右侧控制面板 -->
        <view class="control-panel">
          <button class="audio-btn" @click="toggleAudio" :class="{ 'muted': !audioEnabled }">
            <text class="audio-icon">{{audioEnabled ? '🔊' : '🔇'}}</text>
          </button>

          <button class="bomb-btn" @click="useBomb" :disabled="bombs <= 0">
            <text class="bomb-icon">💣</text>
            <text class="bomb-count">{{bombs}}</text>
          </button>

          <button class="pause-btn" @click="togglePause">
            <text class="pause-icon">{{isPaused ? '▶️' : '⏸️'}}</text>
          </button>
        </view>

        <!-- 关卡进度条 -->
        <view class="progress-bar">
          <view class="progress-bg">
            <view class="progress-fill" :style="{ width: levelProgress + '%' }"></view>
          </view>
          <text class="progress-text">{{Math.floor(levelProgress)}}%</text>
        </view>
      </view>

      <!-- 暂停界面 -->
      <view v-if="isPaused" class="pause-overlay">
        <view class="pause-content">
          <text class="pause-title">游戏暂停</text>

          <view class="pause-stats">
            <text class="stat-item">当前分数: {{score}}</text>
            <text class="stat-item">关卡: {{currentLevel}}</text>
            <text class="stat-item">击败敌机: {{enemiesKilled}}</text>
          </view>

          <view class="pause-actions">
            <button class="resume-btn" @click="togglePause">继续游戏</button>
            <button class="restart-btn" @click="restartLevel">重新开始</button>
            <button class="quit-btn" @click="quitGame">退出游戏</button>
          </view>
        </view>
      </view>

      <!-- 游戏结束界面 -->
      <view v-if="gameOver" class="game-over">
        <view class="game-over-content">
          <text class="game-over-title">{{gameWon ? '关卡完成!' : '游戏结束'}}</text>

          <view class="final-stats">
            <text class="final-score">最终分数: {{score}}</text>
            <text class="level-info">关卡: {{currentLevel}}</text>
            <text class="enemies-info">击败敌机: {{enemiesKilled}}</text>

            <!-- 星级评价 -->
            <view v-if="gameWon" class="star-rating">
              <text class="rating-label">评价:</text>
              <view class="stars">
                <text
                  v-for="star in 3"
                  :key="star"
                  class="star"
                  :class="{ 'earned': star <= starsEarned }"
                >★</text>
              </view>
            </view>
          </view>

          <view class="game-over-actions">
            <button v-if="gameWon && hasNextLevel" class="next-btn" @click="nextLevel">下一关</button>
            <button class="restart-btn" @click="restartLevel">重新开始</button>
            <button class="hall-btn" @click="backToHall">返回大厅</button>
          </view>
        </view>
      </view>

      <!-- 关卡开始提示 -->
      <view v-if="showLevelStart" class="level-start">
        <view class="level-start-content">
          <text class="level-start-title">关卡 {{currentLevel}}</text>
          <text class="level-start-name">{{levelConfig.name}}</text>
          <text class="level-start-desc">{{levelConfig.description}}</text>

          <view class="level-objectives">
            <text class="objectives-title">目标:</text>
            <text
              v-for="objective in levelConfig.objectives"
              :key="objective"
              class="objective-item"
            >• {{objective}}</text>
          </view>

          <button class="start-level-btn" @click="startLevel">开始挑战</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// 导入游戏配置和工具
import { levelConfigs } from './level-configs.js'
import { GameAudioManager } from './audio-manager.js'

export default {
  data() {
    return {
      // 平台检测
      isH5: false,

      // 游戏状态
      gameRunning: false,
      gameOver: false,
      gameWon: false,
      isPaused: false,
      showLevelStart: true,

      // 关卡系统
      currentLevel: 1,
      levelConfig: {},
      levelProgress: 0,
      hasNextLevel: true,
      starsEarned: 0,

      // 游戏数据
      score: 0,
      lives: 3,
      bombs: 3,
      enemiesKilled: 0,

      // 音频控制
      audioEnabled: true,
      audioManager: null,

      // 小程序游戏相关
      ctx: null,
      canvasWidth: 0,
      canvasHeight: 0,

      // 游戏对象
      player: {
        x: 0,
        y: 0,
        width: 60,
        height: 60,
        speed: 5
      },
      bullets: [],
      enemies: [],
      powerUps: [],
      explosions: [],

      // 游戏参数
      lastFired: 0,
      fireRate: 150,
      enemySpawnRate: 1000,
      lastEnemySpawn: 0,
      powerLevel: 1,

      // Phaser相关（H5版本）
      game: null,
      scene: null,

      // 动画帧
      animationFrame: null,
      lastTime: 0,

      // 游戏时间
      levelStartTime: 0,

      // 触摸控制
      touchStartX: 0,
      touchStartY: 0,
      shootingInterval: null
    }
  },

  onLoad(options) {
    // 检测平台
    // #ifdef H5
    this.isH5 = true
    // #endif

    // #ifdef MP-WEIXIN
    this.isH5 = false
    // #endif

    // 获取关卡参数
    if (options.level) {
      this.currentLevel = parseInt(options.level)
    }

    // 加载关卡配置
    this.loadLevelConfig()

    // 加载音频设置
    this.loadAudioSettings()

    // 初始化游戏
    if (this.isH5) {
      this.initPhaserGame()
    } else {
      this.initMiniProgramGame()
    }
  },

  onUnload() {
    this.cleanup()
  },

  methods: {
    // 加载关卡配置
    loadLevelConfig() {
      this.levelConfig = levelConfigs[this.currentLevel] || levelConfigs[1]
      this.hasNextLevel = !!levelConfigs[this.currentLevel + 1]
    },

    // 加载音频设置
    loadAudioSettings() {
      const globalSettings = uni.getStorageSync('gameHall_settings') || {}
      this.audioEnabled = globalSettings.soundEnabled !== undefined ? globalSettings.soundEnabled : true

      // 初始化音频管理器
      this.audioManager = new GameAudioManager(this.audioEnabled)
    },

    // 保存音频设置
    saveAudioSettings() {
      const globalSettings = uni.getStorageSync('gameHall_settings') || {}
      globalSettings.soundEnabled = this.audioEnabled
      uni.setStorageSync('gameHall_settings', globalSettings)
    },

    // 初始化小程序游戏
    initMiniProgramGame() {
      const query = uni.createSelectorQuery().in(this)
      query.select('.game-canvas').boundingClientRect(data => {
        this.canvasWidth = data.width
        this.canvasHeight = data.height

        // 初始化玩家位置
        this.player.x = this.canvasWidth / 2 - this.player.width / 2
        this.player.y = this.canvasHeight - this.player.height - 50

        // 获取画布上下文
        this.ctx = uni.createCanvasContext('gameCanvas', this)

        // 显示关卡开始界面
        this.showLevelStart = true
      }).exec()
    },

    // 开始关卡
    startLevel() {
      this.showLevelStart = false
      this.gameOver = false
      this.gameWon = false
      this.isPaused = false

      // 重置游戏状态
      this.resetGameState()

      // 记录关卡开始时间
      this.levelStartTime = Date.now()

      // 开始游戏循环
      if (!this.isH5) {
        this.startMiniProgramGame()
      }
    },

    // 重置游戏状态
    resetGameState() {
      this.score = 0
      this.lives = this.levelConfig.lives || 3
      this.bombs = this.levelConfig.bombs || 3
      this.enemiesKilled = 0
      this.levelProgress = 0
      this.powerLevel = 1

      // 清空游戏对象
      this.bullets = []
      this.enemies = []
      this.powerUps = []
      this.explosions = []

      // 重置玩家位置
      if (!this.isH5) {
        this.player.x = this.canvasWidth / 2 - this.player.width / 2
        this.player.y = this.canvasHeight - this.player.height - 50
      }
    },

    // 音频控制
    toggleAudio() {
      this.audioEnabled = !this.audioEnabled
      this.saveAudioSettings()

      if (this.audioManager) {
        this.audioManager.setEnabled(this.audioEnabled)
      }

      uni.showToast({
        title: this.audioEnabled ? '音效已开启' : '音效已关闭',
        icon: 'none',
        duration: 1000
      })
    },

    // 游戏控制
    togglePause() {
      this.isPaused = !this.isPaused

      if (this.isPaused) {
        // 暂停游戏循环
        if (this.animationFrame) {
          cancelAnimationFrame(this.animationFrame)
        }
      } else {
        // 恢复游戏循环
        if (!this.isH5 && this.gameRunning) {
          this.gameLoop()
        }
      }
    },

    restartLevel() {
      this.showLevelStart = true
      this.gameRunning = false
      this.gameOver = false
      this.isPaused = false

      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame)
      }
    },

    nextLevel() {
      if (this.hasNextLevel) {
        this.currentLevel++
        this.loadLevelConfig()
        this.restartLevel()
      }
    },

    quitGame() {
      this.cleanup()
      this.backToHall()
    },

    backToHall() {
      // 保存游戏数据
      this.saveGameProgress()

      // 返回游戏大厅
      uni.navigateBack({
        delta: 1
      })
    },

    goBack() {
      if (this.gameRunning && !this.gameOver) {
        // 如果游戏正在进行，先暂停
        this.togglePause()
      } else {
        this.backToHall()
      }
    },

    // 保存游戏进度
    saveGameProgress() {
      const gameData = uni.getStorageSync('gameHall_gameData') || {}

      if (!gameData['thunder-fighter']) {
        gameData['thunder-fighter'] = {
          bestScore: 0,
          unlocked: true,
          levels: []
        }
      }

      const thunderFighterData = gameData['thunder-fighter']

      // 更新最高分
      if (this.score > thunderFighterData.bestScore) {
        thunderFighterData.bestScore = this.score
      }

      // 更新关卡数据
      if (!thunderFighterData.levels[this.currentLevel - 1]) {
        thunderFighterData.levels[this.currentLevel - 1] = {
          id: this.currentLevel,
          unlocked: true,
          completed: false,
          stars: 0,
          bestScore: 0
        }
      }

      const levelData = thunderFighterData.levels[this.currentLevel - 1]

      if (this.gameWon) {
        levelData.completed = true
        if (this.starsEarned > levelData.stars) {
          levelData.stars = this.starsEarned
        }

        // 解锁下一关
        if (this.hasNextLevel && !thunderFighterData.levels[this.currentLevel]) {
          thunderFighterData.levels[this.currentLevel] = {
            id: this.currentLevel + 1,
            unlocked: true,
            completed: false,
            stars: 0,
            bestScore: 0
          }
        }
      }

      if (this.score > levelData.bestScore) {
        levelData.bestScore = this.score
      }

      uni.setStorageSync('gameHall_gameData', gameData)

      // 更新用户统计
      const userData = uni.getStorageSync('gameHall_userData') || { totalScore: 0, gamesPlayed: 0 }
      userData.totalScore += this.score
      userData.gamesPlayed += 1
      uni.setStorageSync('gameHall_userData', userData)
    },

    // 小程序游戏循环
    startMiniProgramGame() {
      this.gameRunning = true
      this.lastTime = Date.now()
      this.gameLoop()
    },

    gameLoop() {
      if (!this.gameRunning || this.isPaused) {
        return
      }

      const currentTime = Date.now()
      const deltaTime = currentTime - this.lastTime
      this.lastTime = currentTime

      // 更新游戏逻辑
      this.updateGame(deltaTime)

      // 渲染游戏
      this.renderGame()

      // 检查游戏状态
      this.checkGameState()

      // 继续游戏循环
      this.animationFrame = requestAnimationFrame(() => this.gameLoop())
    },

    updateGame(deltaTime) {
      // 更新子弹
      this.updateBullets(deltaTime)

      // 更新敌机
      this.updateEnemies(deltaTime)

      // 更新道具
      this.updatePowerUps(deltaTime)

      // 更新爆炸效果
      this.updateExplosions(deltaTime)

      // 生成敌机
      this.spawnEnemies(deltaTime)

      // 生成道具
      this.spawnPowerUps(deltaTime)

      // 检测碰撞
      this.checkCollisions()

      // 更新关卡进度
      this.updateLevelProgress(deltaTime)
    },

    updateBullets(deltaTime) {
      for (let i = this.bullets.length - 1; i >= 0; i--) {
        const bullet = this.bullets[i]
        bullet.y -= bullet.speed * (deltaTime / 16)

        // 移除超出屏幕的子弹
        if (bullet.y < -bullet.height) {
          this.bullets.splice(i, 1)
        }
      }
    },

    updateEnemies(deltaTime) {
      for (let i = this.enemies.length - 1; i >= 0; i--) {
        const enemy = this.enemies[i]
        enemy.y += enemy.speed * (deltaTime / 16)

        // 移除超出屏幕的敌机
        if (enemy.y > this.canvasHeight + enemy.height) {
          this.enemies.splice(i, 1)
        }
      }
    },

    updatePowerUps(deltaTime) {
      for (let i = this.powerUps.length - 1; i >= 0; i--) {
        const powerUp = this.powerUps[i]
        powerUp.y += powerUp.speed * (deltaTime / 16)

        // 移除超出屏幕的道具
        if (powerUp.y > this.canvasHeight + powerUp.height) {
          this.powerUps.splice(i, 1)
        }
      }
    },

    updateExplosions(deltaTime) {
      for (let i = this.explosions.length - 1; i >= 0; i--) {
        const explosion = this.explosions[i]
        explosion.frame += deltaTime / 50 // 动画速度

        // 移除完成的爆炸动画
        if (explosion.frame >= explosion.totalFrames) {
          this.explosions.splice(i, 1)
        }
      }
    },

    spawnEnemies(deltaTime) {
      if (Date.now() - this.lastEnemySpawn > this.levelConfig.enemySpawnRate) {
        this.createEnemy()
        this.lastEnemySpawn = Date.now()
      }
    },

    spawnPowerUps(deltaTime) {
      // 随机生成道具
      if (Math.random() < 0.001 * deltaTime) { // 很低的概率
        this.createPowerUp()
      }
    },

    createEnemy() {
      const enemyTypes = this.levelConfig.enemyTypes || ['basic']
      const type = enemyTypes[Math.floor(Math.random() * enemyTypes.length)]

      const enemy = {
        x: Math.random() * (this.canvasWidth - 40),
        y: -40,
        width: 40,
        height: 40,
        speed: this.levelConfig.enemySpeed || 3,
        health: this.levelConfig.enemyHealth || 1,
        type: type,
        maxHealth: this.levelConfig.enemyHealth || 1
      }

      this.enemies.push(enemy)
    },

    createPowerUp() {
      const powerUp = {
        x: Math.random() * (this.canvasWidth - 30),
        y: -30,
        width: 30,
        height: 30,
        speed: 2,
        type: 'power' // 简化版本，只有一种道具
      }

      this.powerUps.push(powerUp)
    },

    checkCollisions() {
      // 子弹与敌机碰撞
      for (let i = this.bullets.length - 1; i >= 0; i--) {
        const bullet = this.bullets[i]

        for (let j = this.enemies.length - 1; j >= 0; j--) {
          const enemy = this.enemies[j]

          if (this.isColliding(bullet, enemy)) {
            // 移除子弹
            this.bullets.splice(i, 1)

            // 敌机受伤
            enemy.health--

            if (enemy.health <= 0) {
              // 敌机被摧毁
              this.enemies.splice(j, 1)
              this.score += 100
              this.enemiesKilled++

              // 创建爆炸效果
              this.createExplosion(enemy.x, enemy.y)

              // 播放音效
              if (this.audioManager) {
                this.audioManager.playSound('explosion')
              }

              // 有概率掉落道具
              if (Math.random() < this.levelConfig.powerUpRate) {
                this.createPowerUp()
              }
            }

            break
          }
        }
      }

      // 玩家与敌机碰撞
      for (let i = this.enemies.length - 1; i >= 0; i--) {
        const enemy = this.enemies[i]

        if (this.isColliding(this.player, enemy)) {
          // 玩家受伤
          this.lives--
          this.enemies.splice(i, 1)

          // 创建爆炸效果
          this.createExplosion(enemy.x, enemy.y)

          // 播放音效
          if (this.audioManager) {
            this.audioManager.playSound('player_hit')
          }

          // 检查游戏结束
          if (this.lives <= 0) {
            this.endGame(false)
          }
        }
      }

      // 玩家与道具碰撞
      for (let i = this.powerUps.length - 1; i >= 0; i--) {
        const powerUp = this.powerUps[i]

        if (this.isColliding(this.player, powerUp)) {
          // 收集道具
          this.powerUps.splice(i, 1)
          this.collectPowerUp(powerUp)

          // 播放音效
          if (this.audioManager) {
            this.audioManager.playSound('powerup')
          }
        }
      }
    },

    isColliding(obj1, obj2) {
      return obj1.x < obj2.x + obj2.width &&
             obj1.x + obj1.width > obj2.x &&
             obj1.y < obj2.y + obj2.height &&
             obj1.y + obj1.height > obj2.y
    },

    createExplosion(x, y) {
      const explosion = {
        x: x,
        y: y,
        frame: 0,
        totalFrames: 20 // 爆炸动画帧数
      }

      this.explosions.push(explosion)
    },

    collectPowerUp(powerUp) {
      switch (powerUp.type) {
        case 'power':
          this.powerLevel = Math.min(this.powerLevel + 1, 5)
          this.score += 50
          break
        case 'bomb':
          this.bombs++
          break
        case 'life':
          this.lives++
          break
      }
    },

    updateLevelProgress(deltaTime) {
      // 根据时间和击败敌机数量计算进度
      const timeProgress = (Date.now() - this.levelStartTime) / this.levelConfig.duration * 50
      const enemyProgress = (this.enemiesKilled / this.levelConfig.targetEnemies) * 50

      this.levelProgress = Math.min(timeProgress + enemyProgress, 100)

      // 检查关卡完成
      if (this.levelProgress >= 100 || this.enemiesKilled >= this.levelConfig.targetEnemies) {
        this.endGame(true)
      }
    },

    checkGameState() {
      // 检查游戏结束条件
      if (this.lives <= 0) {
        this.endGame(false)
      }
    },

    endGame(won) {
      this.gameRunning = false
      this.gameOver = true
      this.gameWon = won

      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame)
      }

      // 计算星级
      if (won) {
        this.starsEarned = this.calculateStars()

        // 播放胜利音效
        if (this.audioManager) {
          this.audioManager.playSound('level_complete')
        }
      } else {
        // 播放失败音效
        if (this.audioManager) {
          this.audioManager.playSound('game_over')
        }
      }

      // 保存游戏数据
      this.saveGameProgress()
    },

    calculateStars() {
      const thresholds = this.levelConfig.starThresholds || [500, 800, 1000]

      if (this.score >= thresholds[2]) return 3
      if (this.score >= thresholds[1]) return 2
      if (this.score >= thresholds[0]) return 1
      return 0
    },

    // 触摸控制
    onTouchStart(e) {
      if (!this.gameRunning || this.isPaused) return

      const touch = e.touches[0]
      this.touchStartX = touch.x
      this.touchStartY = touch.y

      // 开始自动射击
      this.startShooting()
    },

    onTouchMove(e) {
      if (!this.gameRunning || this.isPaused) return

      const touch = e.touches[0]
      const deltaX = touch.x - this.touchStartX
      const deltaY = touch.y - this.touchStartY

      // 移动玩家
      this.player.x += deltaX
      this.player.y += deltaY

      // 限制玩家在屏幕内
      this.player.x = Math.max(0, Math.min(this.canvasWidth - this.player.width, this.player.x))
      this.player.y = Math.max(0, Math.min(this.canvasHeight - this.player.height, this.player.y))

      this.touchStartX = touch.x
      this.touchStartY = touch.y
    },

    onTouchEnd(e) {
      // 停止射击
      this.stopShooting()
    },

    startShooting() {
      this.shootingInterval = setInterval(() => {
        this.shoot()
      }, this.fireRate)
    },

    stopShooting() {
      if (this.shootingInterval) {
        clearInterval(this.shootingInterval)
        this.shootingInterval = null
      }
    },

    shoot() {
      if (!this.gameRunning || this.isPaused) return

      const bullet = {
        x: this.player.x + this.player.width / 2 - 2,
        y: this.player.y,
        width: 4,
        height: 10,
        speed: 8
      }

      this.bullets.push(bullet)

      // 播放射击音效
      if (this.audioManager) {
        this.audioManager.playSound('shoot')
      }
    },

    useBomb() {
      if (this.bombs <= 0 || !this.gameRunning || this.isPaused) return

      this.bombs--

      // 清除所有敌机
      this.enemies.forEach(enemy => {
        this.score += 50
        this.enemiesKilled++
        this.createExplosion(enemy.x, enemy.y)
      })

      this.enemies = []

      // 播放炸弹音效
      if (this.audioManager) {
        this.audioManager.playSound('bomb')
      }
    },

    renderGame() {
      if (!this.ctx) return

      // 清空画布
      this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

      // 绘制背景
      this.ctx.setFillStyle('#000033')
      this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

      // 绘制星空背景
      this.drawStars()

      // 绘制玩家
      this.drawPlayer()

      // 绘制子弹
      this.drawBullets()

      // 绘制敌机
      this.drawEnemies()

      // 绘制道具
      this.drawPowerUps()

      // 绘制爆炸效果
      this.drawExplosions()

      // 提交绘制
      this.ctx.draw()
    },

    drawStars() {
      this.ctx.setFillStyle('#ffffff')
      for (let i = 0; i < 50; i++) {
        const x = Math.random() * this.canvasWidth
        const y = Math.random() * this.canvasHeight
        this.ctx.fillRect(x, y, 1, 1)
      }
    },

    drawPlayer() {
      this.ctx.setFillStyle('#00ff00')
      this.ctx.fillRect(this.player.x, this.player.y, this.player.width, this.player.height)
    },

    drawBullets() {
      this.ctx.setFillStyle('#ffff00')
      this.bullets.forEach(bullet => {
        this.ctx.fillRect(bullet.x, bullet.y, bullet.width, bullet.height)
      })
    },

    drawEnemies() {
      this.ctx.setFillStyle('#ff0000')
      this.enemies.forEach(enemy => {
        this.ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height)

        // 绘制血条
        if (enemy.health < enemy.maxHealth) {
          const barWidth = enemy.width
          const barHeight = 4
          const healthPercent = enemy.health / enemy.maxHealth

          // 背景
          this.ctx.setFillStyle('#333333')
          this.ctx.fillRect(enemy.x, enemy.y - 8, barWidth, barHeight)

          // 血量
          this.ctx.setFillStyle('#ff0000')
          this.ctx.fillRect(enemy.x, enemy.y - 8, barWidth * healthPercent, barHeight)
        }
      })
    },

    drawPowerUps() {
      this.ctx.setFillStyle('#00ffff')
      this.powerUps.forEach(powerUp => {
        this.ctx.fillRect(powerUp.x, powerUp.y, powerUp.width, powerUp.height)
      })
    },

    drawExplosions() {
      this.ctx.setFillStyle('#ff8800')
      this.explosions.forEach(explosion => {
        const size = 20 + Math.sin(explosion.frame / 5) * 10
        this.ctx.fillRect(explosion.x - size/2, explosion.y - size/2, size, size)
      })
    },

    // 清理资源
    cleanup() {
      this.gameRunning = false

      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame)
      }

      if (this.shootingInterval) {
        clearInterval(this.shootingInterval)
      }

      if (this.game && this.isH5) {
        this.game.destroy(true)
      }

      if (this.audioManager) {
        this.audioManager.cleanup()
      }
    }
  }
}
</script>

<style scoped>
.thunder-fighter {
  width: 100%;
  height: 100vh;
  position: relative;
  background: #000;
}

/* H5版本样式 */
.game-container {
  width: 100%;
  height: 100%;
}

/* 小程序版本样式 */
.miniprogram-game {
  width: 100%;
  height: 100vh;
  position: relative;
}

.game-canvas {
  width: 100%;
  height: 100%;
}

/* 游戏UI样式 */
.game-ui {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: rgba(0, 0, 0, 0.7);
  pointer-events: auto;
}

.back-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 40rpx;
  color: white;
}

.game-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.level-text, .score-text {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

.status-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10rpx;
}

.lives-text, .bombs-text {
  color: white;
  font-size: 24rpx;
}

.control-panel {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  pointer-events: auto;
}

.audio-btn, .bomb-btn, .pause-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}

.audio-btn.muted {
  background: rgba(255, 0, 0, 0.8);
}

.audio-icon, .bomb-icon, .pause-icon {
  font-size: 36rpx;
}

.bomb-count {
  font-size: 20rpx;
  color: #333;
  margin-top: 5rpx;
}

.bomb-btn:disabled {
  opacity: 0.5;
  background: rgba(128, 128, 128, 0.8);
}

.progress-bar {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  pointer-events: auto;
}

.progress-bg {
  flex: 1;
  height: 20rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 10rpx;
  transition: width 0.3s ease;
}

.progress-text {
  color: white;
  font-size: 24rpx;
  min-width: 80rpx;
  text-align: center;
}

/* 暂停界面样式 */
.pause-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.pause-content {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx;
  margin: 40rpx;
  text-align: center;
  max-width: 500rpx;
}

.pause-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
}

.pause-stats {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 60rpx;
}

.stat-item {
  font-size: 32rpx;
  color: #666;
}

.pause-actions {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.resume-btn, .restart-btn, .quit-btn {
  padding: 30rpx 60rpx;
  border-radius: 15rpx;
  border: none;
  font-size: 32rpx;
}

.resume-btn {
  background: #4CAF50;
  color: white;
}

.restart-btn {
  background: #FF9800;
  color: white;
}

.quit-btn {
  background: #f44336;
  color: white;
}

/* 游戏结束界面样式 */
.game-over {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.game-over-content {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx;
  margin: 40rpx;
  text-align: center;
  max-width: 600rpx;
}

.game-over-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
}

.final-stats {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.final-score, .level-info, .enemies-info {
  font-size: 32rpx;
  color: #666;
}

.star-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  margin-top: 30rpx;
}

.rating-label {
  font-size: 28rpx;
  color: #333;
}

.stars {
  display: flex;
  gap: 10rpx;
}

.star {
  font-size: 40rpx;
  color: #ddd;
}

.star.earned {
  color: #ffd700;
}

.game-over-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.next-btn, .hall-btn {
  padding: 30rpx 60rpx;
  border-radius: 15rpx;
  border: none;
  font-size: 32rpx;
}

.next-btn {
  background: #4CAF50;
  color: white;
}

.hall-btn {
  background: #2196F3;
  color: white;
}

/* 关卡开始界面样式 */
.level-start {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.level-start-content {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx;
  margin: 40rpx;
  text-align: center;
  max-width: 600rpx;
}

.level-start-title {
  font-size: 56rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.level-start-name {
  font-size: 36rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.level-start-desc {
  font-size: 28rpx;
  color: #888;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.level-objectives {
  text-align: left;
  margin-bottom: 50rpx;
  padding: 30rpx;
  background: #f5f5f5;
  border-radius: 15rpx;
}

.objectives-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.objective-item {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.start-level-btn {
  padding: 40rpx 80rpx;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 25rpx;
  font-size: 36rpx;
  font-weight: bold;
}
</style>